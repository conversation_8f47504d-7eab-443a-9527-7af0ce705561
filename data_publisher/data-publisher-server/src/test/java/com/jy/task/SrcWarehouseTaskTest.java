package com.jy.task;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.amqp.rabbit.connection.CachingConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = com.jy.DataPublisherApplication.class)
public class SrcWarehouseTaskTest {

    @TestConfiguration
    static class RabbitMQTestConfig {
        @Bean
        public CachingConnectionFactory connectionFactory() {
            CachingConnectionFactory factory = new CachingConnectionFactory();
            factory.setHost("**************"); // Set your RabbitMQ host
            factory.setPort(5672);        // Set your RabbitMQ port
            factory.setUsername("guest"); // Set your RabbitMQ username
            factory.setPassword("guest"); // Set your RabbitMQ password
            return factory;
        }

        @Bean
        public RabbitTemplate rabbitTemplate(CachingConnectionFactory connectionFactory) {
            return new RabbitTemplate(connectionFactory);
        }
    }

    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Test
    public void testSendJsonToSrcWarehouse() {
        String jsonStr = "{\"batchNo\":\"EsPart_100_20250701_114240_20_1\",\"operate\":\"insert\",\"keys\":{\"tableId\":\"19428858f1a14044ae9f3bead705f582\"},\"mainBatchNo\":\"EsPart_100_20250701_114240_20\",\"fields\":{\"oeId\":\"35B4E3B5F79F304AE0630750A8C0B40F\",\"oe\":\"6608219398\",\"stdPartCode\":\"999999\",\"brandId\":\"4028d06d87a310350187e5b6a800358f\",\"brandCode\":\"ADA0\",\"stdPartName\":\"散热器导风罩\",\"searchOe\":\"6608219398\",\"targetTableName\":\"partDetailBrand\",\"seriesId\":\"4028d06d93d909e501955f5ec41f3d56\",\"oeName\":\"散热器导风罩\",\"tableName\":\"part_prod.part_detail_group\"},\"tableName\":\"partDetailBrand\"}";

        // Send to SRC_WAREHOUSE queue
        rabbitTemplate.convertAndSend("SRC_WAREHOUSE", jsonStr);

        System.out.println("JSON message sent to SRC_WAREHOUSE queue successfully");
    }
}
