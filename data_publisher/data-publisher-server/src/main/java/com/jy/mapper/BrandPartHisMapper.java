package com.jy.mapper;

import com.jy.bean.po.BrandPartHis;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * Mapper interface for BrandPartHis operations
 * Supports table sharding based on brand code
 */
@Mapper
public interface BrandPartHisMapper {

    /**
     * Get brand part history by ID
     *
     * @param id        The ID of the brand part history
     * @param brandCode The brand code for table sharding
     * @return The brand part history entity
     */
    BrandPartHis getBrandPartHisById(@Param("id") Long id, @Param("brandCode") String brandCode);

    /**
     * Get brand part history by OE
     *
     * @param oe        The OE code
     * @param brandCode The brand code for table sharding
     * @return The brand part history entity
     */
    BrandPartHis getBrandPartHisByOe(@Param("oe") String oe, @Param("brandCode") String brandCode);

    /**
     * Get brand part history by part ID
     *
     * @param updateId  The part ID
     * @param brandCode The brand code for table sharding
     * @return The brand part history entity
     */
    BrandPartHis getBrandPartHisByUpdateId(@Param("updateId") Long updateId, @Param("brandCode") String brandCode);

    /**
     * List brand part histories by various criteria
     *
     * @param params Map containing query parameters and brandCode for sharding
     * @return List of brand part histories
     */
    List<BrandPartHis> listBrandPartHis(Map<String, Object> params);

    /**
     * List brand part histories by batch number and sup table ID
     *
     * @param batchNo    The batch number
     * @param supTableId The supplier table ID
     * @param brandCode  The brand code for table sharding
     * @return List of brand part histories
     */
    List<BrandPartHis> listByBatchAndSupTableId(
            @Param("batchNo") String batchNo,
            @Param("supTableId") String supTableId,
            @Param("brandCode") String brandCode);

    /**
     * Save a new brand part history
     *
     * @param BrandPartHis The brand part history to save
     */
    void saveBrandPartHis(BrandPartHis BrandPartHis);

    /**
     * Update an existing brand part history
     *
     * @param BrandPartHis The brand part history with updated values
     */
    void updateBrandPartHis(BrandPartHis BrandPartHis);

    /**
     * Update client codes for a brand part history
     *
     * @param id          The ID of the brand part history
     * @param clientCodes The client codes to update
     * @param brandCode   The brand code for table sharding
     */
    void updateClientCodes(@Param("id") Long id, @Param("clientCodes") String clientCodes, @Param("brandCode") String brandCode);

    /**
     * Delete a brand part history by ID
     *
     * @param id        The ID of the brand part history to delete
     * @param brandCode The brand code for table sharding
     */
    void deleteBrandPartHis(@Param("id") Long id, @Param("brandCode") String brandCode);

    /**
     * Get count of history parts for a batch
     *
     * @param batchNo   The batch number
     * @param brandCode The brand code for table sharding
     * @return Count of history parts
     */
    Integer getBatchPartHisCount(@Param("batchNo") String batchNo, @Param("brandCode") String brandCode);

    /**
     * Check if a table exists
     *
     * @param brandCode The name of the table to check
     * @return 1 if the table exists, 0 otherwise
     */
    Integer checkTableExists(@Param("brandCode") String brandCode);


    /**
     * 创建品牌部件历史表
     */
    void createBrandPartHisTable(@Param("brandCode") String brandCode);

    /**
     * 创建批次号和原厂编码联合索引
     */
    void createBatchNoAndOriginalPartCodeIndex(@Param("brandCode") String brandCode);

    /**
     * 创建批次号、原厂编码和更新时间联合索引
     */
    void createBatchNoOriginalPartCodeUtIndex(@Param("brandCode") String brandCode);

    /**
     * 创建批次号和供应商表ID联合索引
     */
    void createBatchNoAndSupTableIdIndex(@Param("brandCode") String brandCode);

    /**
     * 创建原厂编码索引
     */
    void createOriginalPartCodeIndex(@Param("brandCode") String brandCode);

    /**
     * 创建批次号索引
     */
    void createBatchNoIndex(@Param("brandCode") String brandCode);

    /**
     * 创建部件ID索引
     */
    void createUpdateIdIndex(@Param("brandCode") String brandCode);

    int directUpdateClientCodes(Map<String, Object> params);
}
