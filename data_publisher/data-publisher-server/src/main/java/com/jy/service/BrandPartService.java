package com.jy.service;

import com.jy.bean.po.BrandPart;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Service interface for BrandPart operations
 */
public interface BrandPartService {

    /**
     * Get brand part by ID
     * @param id The ID of the brand part
     * @param brandCode The brand code for table sharding
     * @return The brand part entity
     */
    BrandPart getBrandPartById(Long id, String brandCode);

    BrandPart getBrandPartByOe(String oe, String brandCode);

    /**
     * List brand parts by various criteria
     * @param params Map containing query parameters and brandCode for sharding
     * @return List of brand parts
     */
    List<BrandPart> listBrandPart(Map<String, Object> params);

    /**
     * List brand parts by batch number, original part code and update time
     * @param batchNo The batch number
     * @param originalPartCode The original part code
     * @param updateTime The update time
     * @param brandCode The brand code for table sharding
     * @return List of brand parts
     */
    List<BrandPart> listByBatchAndCodeAndTime(String batchNo, String originalPartCode, Date updateTime, String brandCode);

    /**
     * Save a new brand part
     * @param brandPart The brand part to save
     * @return The saved brand part
     */
    BrandPart save(BrandPart brandPart) throws Exception;

    /**
     * Update an existing brand part
     * @param brandPart The brand part with updated values
     * @return The updated brand part
     */
    BrandPart update(BrandPart brandPart) throws Exception;

    /**
     * Delete a brand part by ID
     * @param id The ID of the brand part to delete
     * @param brandCode The brand code for table sharding
     */
    void delete(Long id, String brandCode) throws Exception;

    /**
     * Get count of parts for a batch
     * @param batchNo The batch number
     * @param brandCode The brand code for table sharding
     * @return Count of parts
     */
    Integer getBatchPartCount(String batchNo, String brandCode);

    /**
     * Check if a table exists
     * @param brandCode The name of the table to check
     * @return true if the table exists, false otherwise
     */
    boolean checkTableExists(String brandCode);

    /**
     * Create brand part history table
     * @param brandCode The brand code for table sharding
     */
    void createBrandPartTable(String brandCode) throws Exception;

    /**
     * Create necessary indexes for the brand part table
     * @param brandCode The brand code for table sharding
     */
    void createIndexes(String brandCode) throws Exception;

    /**
     * 根据brandCode和supTableId查询BrandPart
     * @param supTableId 供应商表ID
     * @param brandCode 品牌编码
     * @return BrandPart对象，如果不存在则返回null
     */
    BrandPart getBrandPartBySupTableId(String supTableId, String brandCode);

    /**
     * 根据查询条件分页查询BrandPart列表
     * @param params 查询参数，包含brandCode、startTime、endTime等
     * @return BrandPart列表
     */
    List<BrandPart> queryBrandPartWithPage(Map<String, Object> params);

    /**
     * 保存或更新BrandPart
     * 如果根据brandCode和supTableId查询到记录，则更新；否则新增
     * @param brandPart 要保存或更新的BrandPart对象
     * @return 保存或更新后的BrandPart对象
     * @throws Exception 如果保存或更新失败
     */
    BrandPart saveOrUpdate(BrandPart brandPart) throws Exception;
}
