package com.jy.service;

import com.jy.bean.dto.DashBordDailyDTO;
import com.jy.bean.po.BatchDetail;
import com.jy.bean.po.FlMainBatchInfo;
import org.jetbrains.annotations.NotNull;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @Author: zy
 * @Date: Created in 2019/11/22
 */
public interface FlMainBatchInfoService {
    void saveBacth(@NotNull List<FlMainBatchInfo> mainBatchInfoList) throws Exception;
    void save(@NotNull FlMainBatchInfo mainBatchInfo) throws Exception;
    void update(@NotNull FlMainBatchInfo mainBatchInfo) throws Exception;
    List<FlMainBatchInfo> listFlMainBatchInfo(Map<String, Object> map) throws Exception;

    DashBordDailyDTO getDailyData(String mainBatchNo, String status, String clientCode) throws Exception;

    Integer transferOrder(String mainBatchNo) throws Exception;

    void updateTransferOrder(String mainBatchNo, Integer transferOrder) throws Exception;

    Integer publishOrder(String mainBatchNo);

    void updatePublishOrder(String mainBatchNo, Integer transferOrder);

    FlMainBatchInfo getFlMainBatchInfo(String mainBatchNo);

    void reset(String mainBatchNo, Date startTime);

    void resetBatch(List<BatchDetail> batchDetailList, Date startTime);
}
