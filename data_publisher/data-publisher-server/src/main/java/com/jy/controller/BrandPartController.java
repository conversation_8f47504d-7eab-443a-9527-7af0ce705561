package com.jy.controller;

import com.jy.ann.MethodMonitor;
import com.jy.ann.PageResult;
import com.jy.bean.po.BrandPart;
import com.jy.bean.po.BrandPartHis;
import com.jy.bean.result.JsonResult;
import com.jy.constant.Constant;
import com.jy.service.BrandPartHisService;
import com.jy.service.BrandPartService;
import com.jy.util.EmptyUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.web.bind.annotation.*;

import javax.annotation.security.RolesAllowed;
import java.util.List;
import java.util.Map;

/**
 * BrandPart查询控制器
 */
@RestController
@RolesAllowed(Constant.ROLE_QUERY)
@RequestMapping("/brandPart")
public class BrandPartController {

    @Autowired
    private BrandPartService brandPartService;

    @Autowired
    private BrandPartHisService brandPartHisService;

    /**
     * 分页查询BrandPart列表
     * @param page 分页参数
     * @param paramMap 查询参数
     * @return BrandPart列表
     * @throws Exception
     */
    @PageResult
    @MethodMonitor
    @RequestMapping(value = "query", method = RequestMethod.GET)
    public JsonResult<List<BrandPart>> queryBrandPart(@PageableDefault(size = 100, page = 0) Pageable page,
                                                      @RequestParam Map<String, Object> paramMap) throws Exception {
        // 验证必选参数
        String brandCode = (String) paramMap.get("brandCode");
        if (EmptyUtils.isEmpty(brandCode)) {
            JsonResult<List<BrandPart>> jsonResult = new JsonResult<>();
            jsonResult.setStatus("400");
            jsonResult.setMessage("品牌编码不能为空");
            return jsonResult;
        }

        String startTime = (String) paramMap.get("startTime");
        String endTime = (String) paramMap.get("endTime");
        if (EmptyUtils.isEmpty(startTime) || EmptyUtils.isEmpty(endTime)) {
            JsonResult<List<BrandPart>> jsonResult = new JsonResult<>();
            jsonResult.setStatus("400");
            jsonResult.setMessage("查询时间范围不能为空");
            return jsonResult;
        }

        // 添加分页参数
        paramMap.put("page", page.getPageNumber() * page.getPageSize());
        paramMap.put("size", page.getPageSize());

        List<BrandPart> brandPartList = brandPartService.queryBrandPartWithPage(paramMap);
        JsonResult<List<BrandPart>> jsonResult = new JsonResult<>();
        jsonResult.setResult(brandPartList);
        return jsonResult;
    }

    /**
     * 查询BrandPartHis轨迹列表
     * @param brandCode 品牌编码
     * @param supTableId 供应商表ID
     * @return BrandPartHis轨迹列表
     * @throws Exception
     */
    @MethodMonitor
    @RequestMapping(value = "queryHis", method = RequestMethod.GET)
    public JsonResult<List<BrandPartHis>> queryBrandPartHis(@RequestParam String brandCode,
                                                            @RequestParam String supTableId) throws Exception {
        // 验证必选参数
        if (EmptyUtils.isEmpty(brandCode)) {
            JsonResult<List<BrandPartHis>> jsonResult = new JsonResult<>();
            jsonResult.setStatus("400");
            jsonResult.setMessage("品牌编码不能为空");
            return jsonResult;
        }

        if (EmptyUtils.isEmpty(supTableId)) {
            JsonResult<List<BrandPartHis>> jsonResult = new JsonResult<>();
            jsonResult.setStatus("400");
            jsonResult.setMessage("供应商表ID不能为空");
            return jsonResult;
        }

        List<BrandPartHis> brandPartHisList = brandPartHisService.listByBatchAndSupTableId(null, supTableId, brandCode);
        JsonResult<List<BrandPartHis>> jsonResult = new JsonResult<>();
        jsonResult.setResult(brandPartHisList);
        return jsonResult;
    }
}
