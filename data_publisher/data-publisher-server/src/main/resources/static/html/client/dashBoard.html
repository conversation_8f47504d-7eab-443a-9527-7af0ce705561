<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" xmlns:v-bind="http://www.w3.org/1999/xhtml"
      xmlns:v-on="http://www.w3.org/1999/xhtml"
      xmlns:v-on="http://www.w3.org/1999/xhtml" th:fragment="footer-pages">
<head>
    <meta charset="utf-8">
    <title>layui</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="../../plugin/layui/css/layui.css">
    <link rel="stylesheet" href="../../plugin/layui/css/admin.css">
    <link rel="stylesheet" href="../../css/page-bar.css">

    <style type="text/css">
        .red {
            color: rgb(255, 87, 34) !important;
        }
        .yellow {
            color: rgb(255, 184, 0) !important;
        }
        .green {
            color: rgb(0, 150, 136) !important;
        }
        ul,li{
            /*margin-left: 3px;*/
            padding: 0px;
            font-size: 12px;
        }
        li{
            list-style: none
        }

    </style>
    <!-- 注意：如果你直接复制所有代码到本地，上述css路径需要改成你本地的 -->
</head>
<body>
<div class="layui-body" style="left:0px">
    <div class="layui-fluid">
        <div class="layui-card" id="nav">
            <div class="layui-card-body layui-row layui-col-space10">

                <div class="layui-col-md3">
                    <label class="layui-form-label">主批次号</label>
                    <div class="layui-input-block">
                        <input type="text" class="layui-input" id="mainBatchNo" v-model="searchData.mainBatchNo">
                    </div>
                </div>

                <div class="layui-col-md3">
                    <label class="layui-form-label">状态类型</label>
                    <div class="layui-input-block search-select" id="user-select">
                        <select v-model="searchData.status" :disabled="filter">
                            <option value="0" >全部</option>
                            <option v-cloak v-for="batch in dataTypeList"  :value="batch.code" >{{batch.name}}</option>
                        </select>
                    </div>
                </div>
                <div class="layui-col-md3">
                    <label class="layui-form-label">客户端</label>
                    <div class="layui-input-block search-select" id="user-select">
                        <select v-model="searchData.clientCode" :disabled="filter">
                            <option value="0" >全部</option>
                            <option v-cloak v-for="client in clientList"  :value="client.code" >{{client.name}}</option>
                        </select>
                    </div>
                </div>
                <div class="layui-col-md3">
                    <button class="layui-btn layui-btn-normal" @Click="search()">查询</button>
                </div>
                <div class="layui-col-md5">
                    <div>今日共处理：{{totalCount}} 批次</div>
                </div>
                <div class="layui-col-md5">
                    <div>正在处理：{{totalProcessing}} 批次</div>
                </div>
                <div class="layui-col-md5">
                    <div>转换平台待处理：{{transferReceive}} 批次</div>
                </div>
                <div class="layui-col-md5">
                    <div>转换平台正在处理：{{transferProcessing}} 批次</div>
                </div>
                <div class="layui-col-md5">
                    <div>推送平台正在处理：{{publisherProcessing}} 批次</div>
                </div>
            </div>
        </div>
        <div id="batch">
            <div class="layui-card" >
                <div class="layui-card-header">批次信息</div>
                <div class="layui-card-body layui-row layui-col-space10 ">
                    <table class="layui-table" lay-even="" lay-skin="row">
                        <colgroup>
                            <col width="10">
                            <col width="20">
                            <col width="50">
                            <col width="250">
                            <col width="100">
                            <col width="100">
                            <col width="100">
                            <col width="50">
                        </colgroup>
                        <thead>
                        <tr>
                            <th>
                                <input v-bind:checked="checked" @click="checkAll()" type="checkbox"  style="display: inline-block;">
                            </th>
                            <th>序号</th>
                            <th>主批次号</th>
                            <th>时间段</th>
                            <th>当前状态</th>
                            <th>来源</th>
                            <th>客戶端</th>
                            <!--	<th>进行中（条）</th>-->
                            <th>详情</th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr v-for="(batch, index) in mainBatchList" :key="batch.id" v-bind:class="getStatsClass(batch.status)">
                            <td>
                                <input  type="checkbox" v-model='checkboxList' :value="index">
                            </td>
                            <td v-cloak>{{index + 1}}</td>
                            <td v-cloak>
                                {{batch.mainBatchNo}}
                            </td>
                            <td v-cloak>
                                {{batch.startTimeStr}} - {{batch.endTimeStr}}
                            </td>
                            <td v-cloak>
                                {{getStatusText(batch.status)}}
                            </td>
                            <td v-cloak>
                                {{batch.dataSource=='transfer'?'转换平台':'发布平台'}}
                            </td>
                            <td v-cloak>
                                {{batch.clientCode}}
                            </td>
                            <td v-cloak>
                                <a class="layui-btn layui-btn-normal layui-btn-xs" @Click="toDetail(batch.mainBatchNo, batch.status)">详情</a>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
                <div class="page-bar" style="height: 40px;margin-left: 10px;">
                    <ul>
                        <li><a>总<i>{{pageInfo.total}}</i>条数</a></li>
                    </ul>
                    <div>
                        <ul>
                            <li ><a @click="setPageNo(0)">首页</a></li>
                            <li ><a @click="setPageNo(pageInfo.page - 1)">上一页</a></li>
                            <li v-for="page in pageArray"  v-bind:class="{ 'active': pageInfo.page == page}">
                                <a v-on:click="setPageNo(page)">{{ page + 1 }}</a>
                            </li>
                            <li ><a @click="setPageNo(pageInfo.page + 1)">下一页</a></li>
                            <li ><a @click="setPageNo(pageInfo.pageTotal - 1)">尾页</a></li>
                            <li><a>共<i>{{pageInfo.pageTotal}}</i>页</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script src="../../plugin/jquery/jquery-1.10.1.min.js"></script>
<script src="../../plugin/jquery/moment.min.js"></script>
<script src="../../js/common.js"></script>
<script src="../../plugin/layer/layer.js"></script>
<script src="../../plugin/layui/layui.js"></script>
<script src="../../plugin/layui/lay/modules/form.js"></script>
<script type="text/javascript" src="../../plugin/vue/vue.min.js"></script>
<script type="text/javascript" src="../../plugin/vue/vue-resource.js"></script>

<script type="text/javascript">
    Vue.http.headers.common['Authorization'] = 'Bearer ' + localStorage.token;
    /*初始的page信息*/
    var pageJson = {
        page: 0,
        size: 100,
        total: 0
    };

    var batchVue = new Vue({
        el: '#batch',
        data: {
            mainBatchList: [],
            checkboxList:[],
            pageInfo:{
                page:0,
                size:100,
                total:0,
                pageTotal:0
            },
            pageArray: []
        },
        /*   http: {
               headers: {
                   isPage: "1"
               }
           },*/
        mounted: function () {
        },
        methods:{
            search: function () {
                var _this = this;
                var loadIndex = layerLoad();
                var searchData = _this.getSearchData();
                this.$http.get('/flMainBatchInfo/daily', searchData, {
                    headers: {
                        isPage: "1"
                    }}).then(function (res) {
                    layer.close(loadIndex);
                    var data = res.data;
                    if (data.status == "200") {
                        navVue.totalCount = res.data.result.totalCount;
                        navVue.totalProcessing = res.data.result.totalProcessing;
                        navVue.publisherProcessing = res.data.result.publisherProcessing;
                        navVue.transferProcessing = res.data.result.transferProcessing;
                        navVue.transferReceive = res.data.result.transferReceive;
                        _this.mainBatchList = res.data.result.mainBatchList;
                        _this.setPage(res.data.pageInfo);

                    } else {
                        layer.msg("查询：" + res.data.message);
                        _this.mainBatchList = [];
                        _this.setPage(pageJson);
                    }
                });
                // this.setPage(pageJson);
            },
            getSearchData: function(){
                var searchData = JSON.parse(JSON.stringify(navVue.searchData));
                if(searchData.clientCode == "0"){
                    delete searchData["clientCode"];
                }
                if(searchData.status == "0"){
                    delete searchData["status"];
                }
                if(searchData.status == "0"){
                    delete searchData["status"];
                }
                searchData["page"] = this.pageInfo.page;
                searchData["size"] = this.pageInfo.size;
                return searchData;
            },
            setPage: function(pageJson){
                this.pageInfo.pageTotal = Math.ceil(pageJson.total / pageJson.size);
                this.pageInfo.page = pageJson.page;
                this.pageInfo.size = pageJson.size;
                this.pageInfo.total = pageJson.total;
                this.pageArray = this.getPageArray();
            },
            getPageArray: function () {
                totalPage = this.pageInfo.pageTotal;
                page = this.pageInfo.page;
                var left = 0;
                var right = totalPage - 1;
                var pageArray = [];
                if (totalPage >= 11) {
                    if (page > 5 && page < totalPage - 4) {
                        left = page - 5;
                        right = page + 4;
                    } else {
                        if (page <= 5) {
                            left = 1;
                            right = 10;
                        } else {
                            right = totalPage;
                            left = totalPage - 9;
                        }
                    }
                }
                while (left <= right) {
                    pageArray.push(left);
                    left++;
                }
                return pageArray
            },
            setPageNo: function(pageNo){
                if(pageNo > this.pageInfo.pageTotal - 1){
                    return;
                } else if (pageNo < 0){
                    return;
                }
                this.pageInfo.page = pageNo;
                this.pageArray = this.getPageArray()
                this.search();
            },
            toDetail:function(mainBatchNo, status){
                layer.open({
                    type: 2,
                    skin: 'layui-layer-rim', //加上边框
                    area: ['1700px', '800px'], //宽高
                    title: '批次详情',
                    content: 'dashBoardDetail?mainBatchNo=' + mainBatchNo + '&status=' + status
                });
            },
            getStatsClass:function (status) {
                if(status == "p03"){
                    return "red";
                } else if(status == "p02"){
                    return "green";
                }else{
                    return "yellow";
                }
            },
            //重新发送失败信息
            checkAll: function () {
                if(!this.checked){//全选
                    var _this = this;
                    this.checkboxList = [];
                    this.checked = true;
                    this.batchList.forEach(function(item,index){
                        _this.checkboxList.push(index);
                    });
                }else{//反选
                    this.checkboxList = [];
                    this.checked = false;
                }
            },
            getStatusText: function (status) {
                const statusItem = navVue.dataTypeList.find(item => item.code == status);
                return statusItem ? statusItem.name : '未知状态';
            }

        }
    });

    var navVue = new Vue({
        el: '#nav',
        data: {
            totalCount:0,
            totalProcessing:0,
            publisherProcessing:0,
            transferProcessing:0,
            transferReceive:0,
            dataTypeList: [],
            clientList: [],
            searchData: {
                clientName: "",
                clientCode:"0",
                batchNo: "",
                mainBatchNo: "",
                status: "0",
            }
        },
        mounted: function () {
            this.getClientList();
            this.getDataType();
            //this.getStatisData();
        },
        methods:{
            search: function () {
                batchVue.mainBatchList = [];
                batchVue.search(this.searchData);
            },
            repushAll:  function () {
                batchVue.repushAll();
            },
            getClientList: function(){
                var _this = this;
                var loadIndex = layerLoad();
                this.$http.get('/client/query').then(function (res) {
                    layer.close(loadIndex);
                    var data = res.data;
                    if (data.status == "200") {
                        _this.clientList = res.data.result;
                        // _this.setPage(res.data.pageInfo);
                    } else {
                        layer.msg("获取客户端列表：" + res.data.message);
                    }
                });
            },
            getDataType: function () {
                //成功后回调
                var _this = this;
                _this.dataTypeList = [];
                var loadIndex = layerLoad();
                this.$http.get('/dict?type=dbdType').then(function(res){
                    layer.close(loadIndex);
                    if(res.data.status == "200"){
                        _this.dataTypeList = res.data.result;
                    } else {
                        layer.msg(res.data.message);
                    }
                });
            },
            getStatisData: function () {
                //成功后回调
                var _this = this;
                _this.dataTypeList = [];
                var loadIndex = layerLoad();
                this.$http.get('/flMainBatchInfo/daily').then(function(res){
                    layer.close(loadIndex);
                    if(res.data.status == "200"){
                        _this.totalCount = res.data.result.totalCount;
                        _this.totalProcessing = res.data.result.totalProcessing;
                        _this.publisherProcessing = res.data.result.publisherProcessing;
                        _this.transferProcessing = res.data.result.transferProcessing;
                        _this.transferReceive = res.data.result.transferReceive;
                        batchVue.mainBatchList = res.data.result.mainBatchList;
                    } else {
                        layer.msg(res.data.message);
                    }
                });
            },
        }
    });
</script>

</body>
</html>
