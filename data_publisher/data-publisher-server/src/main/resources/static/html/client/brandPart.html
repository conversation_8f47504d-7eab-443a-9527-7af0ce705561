<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" xmlns:v-bind="http://www.w3.org/1999/xhtml"
	  xmlns:v-on="http://www.w3.org/1999/xhtml"
	  xmlns:v-on="http://www.w3.org/1999/xhtml" th:fragment="footer-pages">
<head>
	<meta charset="utf-8">
	<title>品牌配件查询</title>
	<meta name="renderer" content="webkit">
	<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
	<link rel="stylesheet" href="../../plugin/layui/css/layui.css">
	<link rel="stylesheet" href="../../plugin/layui/css/admin.css">
	<link rel="stylesheet" href="../../css/page-bar.css">

	<style type="text/css">
		.red {
			color: rgb(255, 87, 34) !important;
		}
		.yellow {
			color: rgb(255, 184, 0) !important;
		}
		.green {
			color: rgb(0, 150, 136) !important;
		}
		ul,li{
			padding: 0px;
			font-size: 12px;
		}
		li{
			list-style: none
		}
		.required {
			color: red;
		}
	</style>
</head>
<body>
<div class="layui-body" style="left:0px">
	<div class="layui-fluid">
		<div class="layui-card" id="nav">
			<div class="layui-card-body layui-row layui-col-space10">

				<div class="layui-col-md3">
					<label class="layui-form-label"><span class="required">*</span>品牌编码</label>
					<div class="layui-input-block">
						<input type="text" class="layui-input" id="brandCode" v-model="searchData.brandCode" placeholder="必填">
					</div>
				</div>

				<div class="layui-col-md3">
					<label class="layui-form-label"><span class="required">*</span>时间范围</label>
					<div class="layui-input-block">
						<input type="text" class="layui-input" id="timeRange" v-model="searchData.timeRange" placeholder="必填">
					</div>
				</div>

				<div class="layui-col-md3">
					<label class="layui-form-label">批次号</label>
					<div class="layui-input-block">
						<input type="text" class="layui-input" id="batchNo" v-model="searchData.batchNo" placeholder="可选">
					</div>
				</div>

				<div class="layui-col-md3">
					<button class="layui-btn layui-btn-normal" @Click="search()">查询</button>
					<button class="layui-btn layui-btn-normal" @click="exportData()">导出</button>
				</div>

				<div class="layui-col-md3">
					<label class="layui-form-label">原厂编码</label>
					<div class="layui-input-block">
						<input type="text" class="layui-input" id="originalPartCode" v-model="searchData.originalPartCode" placeholder="可选">
					</div>
				</div>

				<div class="layui-col-md3">
					<label class="layui-form-label"></label>
					<button class="layui-btn layui-btn-normal layui-btn-xs time-range-button" @Click="setTimeRange(-2, this)">2小时</button>
					<button class="layui-btn layui-btn-primary layui-btn-xs time-range-button" @Click="setTimeRange(-6, this)">6小时</button>
					<button class="layui-btn layui-btn-primary layui-btn-xs time-range-button" @Click="setTimeRange(-24, this)">1天</button>
					<button class="layui-btn layui-btn-primary layui-btn-xs time-range-button" @Click="setTimeRange(-24 * 7, this)">7天</button>
					<button class="layui-btn layui-btn-primary layui-btn-xs time-range-button" @Click="setTimeRange(-24 * 30, this)">30天</button>
				</div>

			</div>
		</div>


		<div id="brandPart">
			<div class="layui-card" >
				<div class="layui-card-header">品牌配件信息</div>
				<div class="layui-card-body layui-row layui-col-space10 ">
					<table class="layui-table" lay-even="" lay-skin="row">
						<colgroup>
							<col width="50">
							<col width="100">
							<col width="100">
							<col width="100">
							<col width="150">
							<col width="100">
							<col width="100">
							<col width="100">
							<col width="100">
							<col width="100">
							<col width="100">
						</colgroup>
						<thead>
						<tr>
							<th>序号</th>
							<th>批次号</th>
							<th>品牌编码</th>
							<th>原厂编码</th>
							<th>原厂配件名称</th>
							<th>标准配件编码</th>
							<th>标准配件名称</th>
							<th>状态</th>
							<th>操作</th>
							<th>更新时间</th>
							<th>查看轨迹</th>
						</tr>
						</thead>
						<tbody>
							<tr v-for="(item, index) in brandPartList" :key="item.id">
								<td v-cloak>{{index + 1}}</td>
								<td v-cloak>{{item.batchNo}}</td>
								<td v-cloak>{{item.brandCode}}</td>
								<td v-cloak>{{item.originalPartCode}}</td>
								<td v-cloak>{{item.originalPartName}}</td>
								<td v-cloak>{{item.supPartCode}}</td>
								<td v-cloak>{{item.supPartName}}</td>
								<td v-cloak>{{item.status == '1' ? '有效' : '无效'}}</td>
								<td v-cloak>{{item.operate}}</td>
								<td v-cloak>{{formatDate(item.updateTime)}}</td>
								<td v-cloak>
									<a class="layui-btn layui-btn-normal layui-btn-xs" @Click="viewTrace(item.brandCode, item.supTableId)">查看轨迹</a>
								</td>
							</tr>
							</tbody>
					</table>
				</div>
				<div class="page-bar" style="height: 40px;margin-left: 10px;">
					<ul>
						<li><a>总<i>{{pageInfo.total}}</i>条数</a></li>
					</ul>
					<div>
						<ul>
							<li ><a @click="setPageNo(0)">首页</a></li>
							<li ><a @click="setPageNo(pageInfo.page - 1)">上一页</a></li>
							<li v-for="page in pageArray"  v-bind:class="{ 'active': pageInfo.page == page}">
								<a v-on:click="setPageNo(page)">{{ page + 1 }}</a>
							</li>
							<li ><a @click="setPageNo(pageInfo.page + 1)">下一页</a></li>
							<li ><a @click="setPageNo(pageInfo.pageTotal - 1)">尾页</a></li>
							<li><a>共<i>{{pageInfo.pageTotal}}</i>页</a></li>
						</ul>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
<script src="../../plugin/jquery/jquery-1.10.1.min.js"></script>
<script src="../../plugin/jquery/moment.min.js"></script>
<script src="../../js/common.js"></script>
<script src="../../plugin/layer/layer.js"></script>
<script src="../../plugin/layui/layui.js"></script>
<script src="../../plugin/layui/lay/modules/form.js"></script>
<script type="text/javascript" src="../../plugin/vue/vue.min.js"></script>
<script type="text/javascript" src="../../plugin/vue/vue-resource.js"></script>

<script type="text/javascript">
	Vue.http.headers.common['Authorization'] = 'Bearer ' + localStorage.token;
	/*初始的page信息*/
    var pageJson = {
        page: 0,
        size: 100,
        total: 0
    };

    var brandPartVue = new Vue({
        el: '#brandPart',
        data: {
            brandPartList: [],
            pageInfo:{
                page:0,
                size:100,
                total:0,
                pageTotal:0
            },
            pageArray: []
        },
        mounted: function () {
        },
        methods:{
            search: function () {
                var _this = this;
                var loadIndex = layerLoad();
                var searchData = _this.getSearchData();
                this.$http.get('/brandPart/query', searchData, {
					headers: {
						isPage: "1"
					}}).then(function (res) {
                    layer.close(loadIndex);
                    var data = res.data;
                    if (data.status == "200") {
                        _this.brandPartList = res.data.result;
                        _this.setPage(res.data.pageInfo);

                    } else {
                        layer.msg("查询：" + res.data.message);
                        _this.brandPartList = [];
                        _this.setPage(pageJson);
                    }
                });
            },
			getSearchData: function(){
				var searchData = JSON.parse(JSON.stringify(navVue.searchData));
                searchData.startTime = DateUtil.formatLongToDate(searchData.startTime);
                searchData.endTime = DateUtil.formatLongToDate(searchData.endTime);
                searchData["page"] = this.pageInfo.page;
                searchData["size"] = this.pageInfo.size;
                return searchData;
			},
            setPage: function(pageJson){
                this.pageInfo.pageTotal = Math.ceil(pageJson.total / pageJson.size);
                this.pageInfo.page = pageJson.page;
                this.pageInfo.size = pageJson.size;
                this.pageInfo.total = pageJson.total;
                this.pageArray = this.getPageArray();
            },
            getPageArray: function () {
                totalPage = this.pageInfo.pageTotal;
                page = this.pageInfo.page;
                var left = 0;
                var right = totalPage - 1;
                var pageArray = [];
                if (totalPage >= 11) {
                    if (page > 5 && page < totalPage - 4) {
                        left = page - 5;
                        right = page + 4;
                    } else {
                        if (page <= 5) {
                            left = 1;
                            right = 10;
                        } else {
                            right = totalPage;
                            left = totalPage - 9;
                        }
                    }
                }
                while (left <= right) {
                    pageArray.push(left);
                    left++;
                }
                return pageArray
            },
            setPageNo: function(pageNo){
                if(pageNo > this.pageInfo.pageTotal - 1){
                    return;
                } else if (pageNo < 0){
                    return;
                }
                this.pageInfo.page = pageNo;
                this.pageArray = this.getPageArray()
				this.search();
            },
            viewTrace:function(brandCode, supTableId){
                layer.open({
                    type: 2,
                    skin: 'layui-layer-rim',
                    area: ['1000px', '600px'],
					title: '品牌配件轨迹',
                    content: '/page/brandPartHis?brandCode=' + brandCode + '&supTableId=' + supTableId
                });
            },
			formatDate: function(dateStr) {
				if (!dateStr) return '';
				return moment(dateStr).format('YYYY-MM-DD HH:mm:ss');
			},
			/*前端生成excel*/
			exportData: function () {
				let _this = this;
				var searchData = _this.getSearchData();
				this.$http.get('/brandPart/query', searchData, {
					headers: {
						isPage: "0"
					}}).then(function (res) {
					var data = res.data;
					if (data.status == "200") {
						var brandParts = res.data.result;
						var excel = '<table>';
						var row = "<tr >";
						//设置表头

						var keys = ["序号", "批次号", "品牌编码", "原厂编码", "原厂配件名称", "标准配件编码", "标准配件名称", "状态", "操作", "更新时间"]
						keys.forEach(function (item) {
							row += "<td width='150'  style='border:1px solid;font-size:18px;background: yellow'>" + item + '</td>';
						});
						//换行
						excel += row + "</tr>";
						//设置数据
						for (let i = 0; i < brandParts.length; i++) {
							let brandPart = brandParts[i];
							var row = "<tr style='font-size:15px;'>";
							row += '<td >' + (i + 1) + '</td>';
							row += '<td>' + (brandPart.batchNo || '') + '</td>';
							row += '<td>' + (brandPart.brandCode || '') + '</td>';
							row += '<td>' + (brandPart.originalPartCode || '') + '</td>';
							row += '<td>' + (brandPart.originalPartName || '') + '</td>';
							row += '<td>' + (brandPart.supPartCode || '') + '</td>';
							row += '<td>' + (brandPart.supPartName || '') + '</td>';
							row += '<td>' + (brandPart.status == '1' ? '有效' : '无效') + '</td>';
							row += '<td>' + (brandPart.operate || '') + '</td>';
							row += '<td>' + _this.formatDate(brandPart.updateTime) + '</td>';
							excel += row + "</tr>";
						}
						excel += "</table>";
						var excelFile = "<html xmlns:o='urn:schemas-microsoft-com:office:office' xmlns:x='urn:schemas-microsoft-com:office:excel' xmlns='http://www.w3.org/TR/REC-html40'>";
						excelFile += '<meta http-equiv="content-type" content="application/vnd.ms-excel; charset=UTF-8">';
						excelFile += '<meta http-equiv="content-type" content="application/vnd.ms-excel';
						excelFile += '; charset=UTF-8">';
						excelFile += "<head>";
						excelFile += "<!--[if gte mso 9]>";
						excelFile += "<xml>";
						excelFile += "<x:ExcelWorkbook>";
						excelFile += "<x:ExcelWorksheets>";
						excelFile += "<x:ExcelWorksheet>";
						excelFile += "<x:Name>";
						excelFile += "品牌配件查询导出";
						excelFile += "</x:Name>";
						excelFile += "<x:WorksheetOptions>";
						excelFile += "<x:DisplayGridlines/>";
						excelFile += "</x:WorksheetOptions>";
						excelFile += "</x:ExcelWorksheet>";
						excelFile += "</x:ExcelWorksheets>";
						excelFile += "</x:ExcelWorkbook>";
						excelFile += "</xml>";
						excelFile += "<![endif]-->";
						excelFile += "</head>";
						excelFile += "<body>";
						excelFile += excel;
						excelFile += "</body>";
						excelFile += "</html>";

						var uri = 'data:application/vnd.ms-excel;charset=utf-8,' + encodeURIComponent(excelFile);

						var link = document.createElement("a");
						link.href = uri;

						link.style = "visibility:hidden";
						link.download = "品牌配件查询导出"+ searchData.startTime + "-"  + searchData.endTime + ".xls";

						document.body.appendChild(link);
						link.click();
						document.body.removeChild(link);
					} else {
						layer.msg(res.data.message);
					}
				});

			}

        }
    });

    var navVue = new Vue({
        el: '#nav',
        data: {
            searchData: {
                brandCode: "",
                batchNo: "",
                originalPartCode: "",
                startTime: "",
                endTime: "",
                timeRange: ""
            }
        },
        mounted: function () {
            this.setTimeRange(-2);
        },
        methods:{
            search: function () {
				// 验证必填字段
				if (!this.searchData.brandCode) {
					layer.msg("品牌编码不能为空");
					return;
				}
				if (!this.searchData.timeRange) {
					layer.msg("时间范围不能为空");
					return;
				}
				brandPartVue.brandPartList = [];
                brandPartVue.search(this.searchData);
            },
            setTimeRange(hours, button) {
                var endDate = DateUtil.getCurrentlyDate();
                var startDate = DateUtil.addSpacingDate(endDate, hours * 60 * 60 * 1000);
                this.searchData.timeRange = startDate + " - " + endDate;
                this.searchData.startTime = DateUtil.strDateToLong(startDate);
                this.searchData.endTime = DateUtil.strDateToLong(endDate);
                if (button) {
                    $(".time-range-button").removeClass("layui-btn-normal").addClass("layui-btn-primary")
                    button.document.activeElement.classList.remove("layui-btn-primary");
                    button.document.activeElement.classList.add("layui-btn-normal");
                }
            },
			/*前端生成excel*/
			exportData: function () {
				brandPartVue.exportData(this.searchData);
			}
        }
    });

    layui.use('laydate', function(){
        var laydate = layui.laydate;

		//执行一个laydate实例
		laydate.render({
			elem: '#timeRange' //指定元素
			, type: 'datetime'
			, range: true
			, done: function (value, date, endDate) {//控件选择完毕后的回调---点击日期、清空、现在、确定均会触发。
				navVue.searchData.timeRange = value;
				navVue.searchData.startTime = DateUtil.strDateToLong(DateUtil.formatLayDate(date));
				navVue.searchData.endTime = DateUtil.strDateToLong(DateUtil.formatLayDate(endDate));

				$(".time-range-button").removeClass("layui-btn-normal").addClass("layui-btn-primary")
			}
		});
    });

</script>

</body>
</html>
