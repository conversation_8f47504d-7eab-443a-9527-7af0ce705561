<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jy.mapper.BrandPartHisMapper">

    <sql id="Base_Column_List">
        id, update_id, batch_no, sup_table_id, sup_part_id, sup_part_code, sup_part_name,
        original_part_id, original_part_name, original_part_code, original_short_name, brand_id,
        brand_code, status, table_name, operate, client_codes, create_time, update_time
    </sql>

    <sql id="Table_Name">
        d_brand_part_his_${brandCode}
    </sql>

    <select id="getBrandPartHisById" resultType="com.jy.bean.po.BrandPartHis">
        select
        <include refid="Base_Column_List"/>
        from <include refid="Table_Name"/>
        where id = #{id}
    </select>
    <select id="getBrandPartHisByOe" resultType="com.jy.bean.po.BrandPartHis">
        select
        <include refid="Base_Column_List"/>
        from <include refid="Table_Name"/>
        where original_part_code = #{oe}
    </select>

    <select id="getBrandPartHisByUpdateId" resultType="com.jy.bean.po.BrandPartHis">
        select
        <include refid="Base_Column_List"/>
        from <include refid="Table_Name"/>
        where update_id = #{updateId}
    </select>

    <select id="listBrandPartHis" resultType="com.jy.bean.po.BrandPartHis">
        select
        <include refid="Base_Column_List"/>
        from <include refid="Table_Name"/>
        <where>
            1=1
            <if test="_parameter.containsKey('batchNo') and batchNo != null and batchNo != ''">
                and batch_no = #{batchNo}
            </if>
            <if test="_parameter.containsKey('updateId') and updateId != null">
                and update_id = #{updateId}
            </if>
            <if test="_parameter.containsKey('originalPartCode') and originalPartCode != null and originalPartCode != ''">
                and original_part_code = #{originalPartCode}
            </if>
            <if test="_parameter.containsKey('supTableId') and supTableId != null and supTableId != ''">
                and sup_table_id = #{supTableId}
            </if>
            <if test="_parameter.containsKey('brandId') and brandId != null and brandId != ''">
                and brand_id = #{brandId}
            </if>
            <if test="_parameter.containsKey('status') and status != null and status != ''">
                and status = #{status}
            </if>
            <if test="_parameter.containsKey('clientCodes') and clientCodes != null and clientCodes != ''">
                and client_codes like CONCAT('%', #{clientCodes}, '%')
            </if>
        </where>
    </select>

    <select id="listByBatchAndSupTableId" resultType="com.jy.bean.po.BrandPartHis">
        select
        <include refid="Base_Column_List"/>
        from <include refid="Table_Name"/>
        where batch_no = #{batchNo}
        <if test="supTableId != null and supTableId != ''">
            and sup_table_id = #{supTableId}
        </if>
        order by update_time desc
    </select>

    <insert id="saveBrandPartHis" parameterType="com.jy.bean.po.BrandPartHis">
        insert into <include refid="Table_Name"/> (
            id, update_id, batch_no, sup_table_id, sup_part_id, sup_part_code, sup_part_name,
            original_part_name, original_part_code, original_short_name, brand_id,
            brand_code, status, table_name, operate, client_codes, original_part_id
        ) values (
            #{id}, #{updateId}, #{batchNo}, #{supTableId}, #{supPartId}, #{supPartCode}, #{supPartName},
            #{originalPartName}, #{originalPartCode}, #{originalShortName}, #{brandId},
            #{brandCode}, #{status}, #{tableName}, #{operate}, #{clientCodes}, #{originalPartId}
        )
    </insert>

    <update id="updateBrandPartHis" parameterType="com.jy.bean.po.BrandPartHis">
        update <include refid="Table_Name"/>
        <set>
            <if test="supTableId != null">sup_table_id = #{supTableId},</if>
            <if test="supPartId != null">sup_part_id = #{supPartId},</if>
            <if test="supPartCode != null">sup_part_code = #{supPartCode},</if>
            <if test="supPartName != null">sup_part_name = #{supPartName},</if>
            <if test="originalPartName != null">original_part_name = #{originalPartName},</if>
            <if test="originalShortName != null">original_short_name = #{originalShortName},</if>
            <if test="status != null">status = #{status},</if>
            <if test="operate != null">operate = #{operate},</if>
            <if test="clientCodes != null">client_codes = #{clientCodes},</if>
            <if test="originalPartId != null">original_part_id = #{originalPartId},</if>
            <if test="batchNo != null">batch_no = #{batchNo},</if>
            <if test="originalPartCode != null">original_part_code = #{originalPartCode},</if>
            <if test="brandId != null">brand_id = #{brandId},</if>
            <if test="brandCode != null">brand_code = #{brandCode},</if>
            <if test="tableName != null">table_name = #{tableName},</if>
            <if test="clientCodes != null">client_codes = #{clientCodes},</if>
        </set>
        where id = #{id}
    </update>

    <update id="updateClientCodes">
        update <include refid="Table_Name"/>
        set client_codes = #{clientCodes}
        where id = #{id}
    </update>

    <delete id="deleteBrandPartHis">
        delete from <include refid="Table_Name"/> where id = #{id}
    </delete>

    <select id="getBatchPartHisCount" resultType="java.lang.Integer">
        select count(1) from <include refid="Table_Name"/> where batch_no = #{batchNo}
    </select>

    <!-- Check if a table exists -->
    <select id="checkTableExists" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM information_schema.tables
        WHERE table_name = 'd_brand_part_${brandCode}'
    </select>

    <!-- 创建表结构 -->
    <update id="createBrandPartHisTable">
        CREATE TABLE <include refid="Table_Name"/>
        (
        id                  varchar(64)                         comment '主键id' primary key,
        update_id           varchar(64)                         not null comment '更新ID',
        batch_no            varchar(128)                        not null comment '小批次号',
        sup_table_id        varchar(64)                         null comment '配件id，根据oe与品牌id生成',
        sup_part_id         varchar(64)                         null comment '标准配件ID',
        sup_part_code       varchar(64)                         null comment '标准配件编码',
        sup_part_name       varchar(200)                        null comment '标准配件名称',
        original_part_id    varchar(64)                        not null comment '原厂配件id',
        original_part_name  varchar(200)                        not null comment '原厂配件名称',
        original_part_code  varchar(64)                         not null comment '原厂配件编码 OE号',
        original_short_name varchar(64)                         not null comment '去除特殊符号OE',
        brand_id            varchar(64)                         not null comment '品牌ID',
        brand_code          varchar(64)                         not null comment '品牌编码',
        status              varchar(1)                          not null comment '是否有效 0:无效 1:有效',
        table_name          varchar(100)                        not null comment '表名',
        operate             varchar(20)                         not null comment '操作',
        client_codes        varchar(100)                        null comment '已更新的客户编码',
        create_time         timestamp default CURRENT_TIMESTAMP not null,
        update_time         timestamp default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP
        )
    </update>

    <!-- 创建批次号和原厂编码联合索引 -->
    <update id="createBatchNoAndOriginalPartCodeIndex">
        create index idx_d_brand_part_his_${brandCode}_batch_original_code
        on <include refid="Table_Name"/> (batch_no, original_part_code)
    </update>

    <!-- 创建批次号、原厂编码和更新时间联合索引 -->
    <update id="createBatchNoOriginalPartCodeUtIndex">
        create index idx_d_brand_part_his_${brandCode}_batch_original_ut
        on <include refid="Table_Name"/> (batch_no, original_part_code, update_time)
    </update>

    <!-- 创建批次号和供应商表ID联合索引 -->
    <update id="createBatchNoAndSupTableIdIndex">
        create index idx_d_brand_part_his_${brandCode}_batch_sup_id
        on <include refid="Table_Name"/> (batch_no, sup_table_id)
    </update>

    <!-- 创建原厂编码索引 -->
    <update id="createOriginalPartCodeIndex">
        create index idx_d_brand_part_his_${brandCode}_original_code
        on <include refid="Table_Name"/> (original_part_code)
    </update>

    <!-- 创建批次号索引 -->
    <update id="createBatchNoIndex">
        create index idx_d_brand_part_his_${brandCode}_batch_no
        on <include refid="Table_Name"/> (batch_no)
    </update>

    <!-- 创建部件ID索引 -->
    <update id="createUpdateIdIndex">
        create index idx_d_brand_part_his_${brandCode}_update_id
        on <include refid="Table_Name"/> (update_id)
    </update>

    <update id="directUpdateClientCodes" parameterType="map">
        update <include refid="Table_Name"/>
        set client_codes = CONCAT(IFNULL(client_codes, ''), ',', #{clientCode})
        where batch_no = #{batchNo}
        <if test="updateId != null and updateId != ''">
            and update_id = #{updateId}
        </if>
        <if test="supTableId != null and supTableId != ''">
            and sup_table_id = #{supTableId}
        </if>
    </update>
</mapper>
